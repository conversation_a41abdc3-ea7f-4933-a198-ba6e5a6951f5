"""Main CLI interface with Rich UI components."""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, Optional

from prompt_toolkit import PromptSession
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.key_binding import KeyBindings
from rich.console import Console
from rich.layout import Layout
from rich.live import Live
from rich.markdown import Markdown
from rich.panel import Panel
from rich.syntax import Syntax
from rich.text import Text

from ai_cli.config.models import Config
from ai_cli.core.orchestrator import Orchestrator
from ai_cli.ui.animation import create_ball_animation
from ai_cli.ui.diff_viewer import DiffViewer

logger = logging.getLogger(__name__)


class CLI:
    """Main CLI interface with Rich UI components."""

    def __init__(self, orchestrator: Orchestrator, config: Config) -> None:
        """Initialize CLI interface.

        Args:
            orchestrator: Core orchestrator instance
            config: Application configuration
        """
        self.orchestrator = orchestrator
        self.config = config
        self.console = Console()
        self.diff_viewer = DiffViewer(self.console)

        # Setup prompt session
        self.history = InMemoryHistory()
        self.prompt_session = PromptSession(
            history=self.history,
            completer=self._create_completer(),
            key_bindings=self._create_key_bindings(),
        )

        # State
        self.running = True

        logger.info("CLI interface initialized")

    async def run(self) -> None:
        """Run the main CLI loop."""
        # Initialize async components
        await self.orchestrator.initialize()

        self._show_welcome()

        try:
            while self.running:
                try:
                    # Get user input
                    user_input = await self._get_user_input()

                    if not user_input.strip():
                        continue

                    # Process input with animation
                    async with create_ball_animation(self.console, "Processing your request"):
                        response = await self.orchestrator.process_input(user_input)

                    # Display response
                    await self._display_response(response)

                except KeyboardInterrupt:
                    self.console.print("\n[yellow]Use /exit to quit or Ctrl+D[/yellow]")
                    continue
                except EOFError:
                    break

        except Exception as e:
            logger.error(f"CLI error: {e}")
            self.console.print(f"[red]Unexpected error: {e}[/red]")
        finally:
            await self._cleanup()

    def _show_welcome(self) -> None:
        """Display welcome message."""
        welcome_text = """
# 🤖 AI-Powered CLI Terminal Tool

Welcome to your intelligent command-line assistant! I can help you with:

- **File Operations**: Read, write, and manipulate files
- **Shell Commands**: Execute system commands safely
- **Web Search**: Find information online
- **Code Analysis**: Understand and refactor code
- **Complex Tasks**: Break down and execute multi-step operations

**Quick Start:**
- Type your request in natural language
- Use `/help` for available commands
- Use `/session start` to begin a new session

**Examples:**
- `list all python files in the current directory`
- `search for "async python patterns" and summarize`
- `refactor main.py to add type hints`

Let's get started! 🚀
"""

        panel = Panel(
            Markdown(welcome_text),
            title="[bold blue]AI CLI Terminal[/bold blue]",
            border_style="blue",
        )

        self.console.print(panel)
        self.console.print()

    async def _get_user_input(self) -> str:
        """Get user input with prompt.

        Returns:
            User input string
        """
        # Create prompt with current session info
        session = self.orchestrator.session_manager.get_current_session()
        session_info = f"({session.name})" if session else ""

        prompt_text = f"ai-cli{session_info}> "

        # Use asyncio to run prompt_toolkit in thread
        loop = asyncio.get_event_loop()
        user_input = await loop.run_in_executor(
            None,
            lambda: self.prompt_session.prompt(prompt_text)
        )

        return user_input.strip()

    async def _display_response(self, response: Dict[str, Any]) -> None:
        """Display response from orchestrator.

        Args:
            response: Response dictionary from orchestrator
        """
        response_type = response.get("type", "response")
        content = response.get("content", "")

        if response_type == "error":
            self._display_error(content)
        elif response_type == "help":
            self._display_help(content)
        elif response_type == "session":
            self._display_session_info(content)
        elif response_type == "context":
            self._display_context_info(content)
        elif response_type == "config":
            self._display_config_info(content)
        elif response_type == "history":
            self._display_history(content)
        elif response_type == "clear":
            self.console.clear()
        elif response_type == "exit":
            self.console.print(f"[yellow]{content}[/yellow]")
            self.running = False
        elif response_type == "autonomous_response":
            self._display_autonomous_response(response)
        elif response_type == "fallback_response":
            self._display_fallback_response(response)
        elif response_type == "diff":
            self._display_diff(response)
        else:
            self._display_regular_response(content, response)

    def _display_error(self, content: str) -> None:
        """Display error message.

        Args:
            content: Error message
        """
        panel = Panel(
            content,
            title="[bold red]Error[/bold red]",
            border_style="red",
        )
        self.console.print(panel)

    def _display_help(self, content: str) -> None:
        """Display help content.

        Args:
            content: Help content in markdown
        """
        panel = Panel(
            Markdown(content),
            title="[bold blue]Help[/bold blue]",
            border_style="blue",
        )
        self.console.print(panel)

    def _display_session_info(self, content: str) -> None:
        """Display session information.

        Args:
            content: Session info content
        """
        panel = Panel(
            content,
            title="[bold cyan]Session[/bold cyan]",
            border_style="cyan",
        )
        self.console.print(panel)

    def _display_context_info(self, content: str) -> None:
        """Display context information.

        Args:
            content: Context info content
        """
        panel = Panel(
            content,
            title="[bold magenta]Context[/bold magenta]",
            border_style="magenta",
        )
        self.console.print(panel)

    def _display_config_info(self, content: str) -> None:
        """Display configuration information.

        Args:
            content: Config info content
        """
        panel = Panel(
            Markdown(content),
            title="[bold yellow]Configuration[/bold yellow]",
            border_style="yellow",
        )
        self.console.print(panel)

    def _display_history(self, content: str) -> None:
        """Display conversation history.

        Args:
            content: History content
        """
        panel = Panel(
            Markdown(content),
            title="[bold blue]History[/bold blue]",
            border_style="blue",
        )
        self.console.print(panel)

    def _display_regular_response(self, content: str, response: Dict[str, Any]) -> None:
        """Display regular AI response.

        Args:
            content: Response content
            response: Full response dictionary
        """
        # Add timestamp if enabled
        timestamp = ""
        if self.config.ui.show_timestamps:
            timestamp = f" - {datetime.now().strftime('%H:%M:%S')}"

        # Add provider info
        provider = response.get("provider", "")
        provider_info = f" ({provider})" if provider else ""

        title = f"[bold green]AI Assistant{provider_info}{timestamp}[/bold green]"

        # Try to render as markdown, fallback to plain text
        try:
            rendered_content = Markdown(content)
        except Exception:
            rendered_content = content

        panel = Panel(
            rendered_content,
            title=title,
            border_style="green",
        )
        self.console.print(panel)

    def _display_autonomous_response(self, response: Dict[str, Any]) -> None:
        """Display autonomous task execution response.

        Args:
            response: Response dictionary with execution details
        """
        content = response.get("content", "")
        execution_log = response.get("execution_log", [])
        iterations = response.get("iterations", 0)
        provider = response.get("provider", "")

        # Add timestamp
        timestamp = ""
        if self.config.ui.show_timestamps:
            timestamp = f" - {datetime.now().strftime('%H:%M:%S')}"

        provider_info = f" ({provider})" if provider else ""
        title = f"[bold green]AI Assistant - Autonomous Execution{provider_info}{timestamp}[/bold green]"

        # Create content with execution summary
        display_content = content

        if execution_log:
            display_content += f"\n\n**Execution completed in {iterations} step(s)**"

        try:
            rendered_content = Markdown(display_content)
        except Exception:
            rendered_content = display_content

        panel = Panel(
            rendered_content,
            title=title,
            border_style="green",
        )
        self.console.print(panel)

    def _display_diff(self, response: Dict[str, Any]) -> None:
        """Display diff content.

        Args:
            response: Response dictionary with diff data
        """
        old_text = response.get("old_text", "")
        new_text = response.get("new_text", "")
        old_label = response.get("old_label", "Original")
        new_label = response.get("new_label", "Modified")
        syntax = response.get("syntax")

        self.console.print(f"\n[bold blue]Diff: {old_label} vs {new_label}[/bold blue]")
        self.diff_viewer.show_diff(old_text, new_text, old_label, new_label, syntax=syntax)

    def _create_completer(self) -> WordCompleter:
        """Create command completer for prompt.

        Returns:
            Word completer instance
        """
        commands = [
            "/help", "/session", "/context", "/config", "/history", "/clear", "/exit", "/quit",
            "list", "read", "write", "search", "refactor", "analyze", "create", "delete",
            "move", "copy", "run", "execute", "install", "update", "git", "docker",
        ]

        return WordCompleter(commands, ignore_case=True)

    def _create_key_bindings(self) -> KeyBindings:
        """Create key bindings for prompt.

        Returns:
            Key bindings instance
        """
        kb = KeyBindings()

        @kb.add('c-c')
        def _(event):
            """Handle Ctrl+C"""
            event.app.exit(exception=KeyboardInterrupt)

        @kb.add('c-d')
        def _(event):
            """Handle Ctrl+D"""
            event.app.exit(exception=EOFError)

        return kb

    async def _cleanup(self) -> None:
        """Cleanup CLI resources."""
        self.console.print("\n[yellow]Shutting down...[/yellow]")
        await self.orchestrator.shutdown()
        self.console.print("[green]Goodbye! 👋[/green]")
